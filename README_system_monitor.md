# 系统监控 AI 分析工具

这个工具集成了系统性能监控和 AI 智能分析功能，能够自动检测系统异常并提供专业的优化建议。

## 文件说明

### 核心文件
- `agent_tool.py` - 系统性能监控核心模块
- `system_monitor_prompt.md` - AI 分析的详细 prompt 模板
- `system_monitor_agent.py` - 本地智能分析代理
- `ai_system_monitor.py` - AI 模型集成示例

### 测试文件
- `test_system_monitor.py` - 基础功能测试

## 功能特性

### 🔍 系统监控
- **CPU 监控**: 使用率、进程分析
- **内存监控**: 使用率、进程内存占用
- **GPU 监控**: 使用率、显存、温度
- **磁盘监控**: 使用率、I/O 统计
- **网络监控**: 连接数、错误统计

### 🤖 AI 智能分析
- **自动告警**: 按严重程度分类问题
- **根因分析**: 分析性能异常原因
- **优化建议**: 提供具体解决方案
- **趋势预测**: 识别潜在风险

## 使用方法

### 方法一：基础监控（无 AI）
```bash
python test_system_monitor.py
```

### 方法二：本地智能分析
```bash
python system_monitor_agent.py
```

### 方法三：AI 模型集成
```bash
python ai_system_monitor.py
```

## AI 模型集成

### 支持的 AI 模型
1. **OpenAI GPT** (ChatGPT/GPT-4)
2. **Anthropic Claude**
3. **本地 LLM** (Ollama, LM Studio 等)
4. **其他 API** (可自定义)

### 配置 AI API

#### OpenAI 配置
```python
import openai
openai.api_key = "your-api-key-here"

# 在 ai_system_monitor.py 中取消注释：
analysis_result = analyze_with_ai_model(system_data, call_openai_api)
```

#### Claude 配置
```python
import anthropic
client = anthropic.Anthropic(api_key="your-api-key-here")

# 在 ai_system_monitor.py 中取消注释：
analysis_result = analyze_with_ai_model(system_data, call_claude_api)
```

#### 本地 LLM 配置
```bash
# 安装 Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull llama2

# 在 ai_system_monitor.py 中取消注释：
analysis_result = analyze_with_ai_model(system_data, call_local_llm_api)
```

## 告警级别

### 🔴 严重告警
- CPU 使用率 > 90%
- 内存使用率 > 90%
- GPU 温度 > 85°C
- 磁盘使用率 > 95%
- 大量网络错误

### 🟡 警告
- CPU 使用率 60-90%
- 内存使用率 60-90%
- GPU 使用率 > 80% 或温度 60-85°C
- 磁盘使用率 60-95%

### 🟢 正常
- 所有指标在正常范围内

## 依赖安装

```bash
# 基础依赖
pip install psutil

# GPU 监控（可选）
pip install GPUtil nvidia-ml-py3

# AI 模型 API（可选）
pip install openai anthropic requests
```

## 输出示例

### 正常状态
```
✅ 系统运行正常
所有性能指标均在正常范围内，无需特别关注。
```

### 异常状态
```
🖥️ 系统监控报告 - 2024-01-15 14:30:25
==================================================

🔴 严重告警：
- CPU使用率过高: 95.2%
  💡 建议：立即检查CPU占用最高的进程，考虑结束不必要的程序

🟡 警告：
- 内存使用率过高: 78.5%
  💡 建议：清理内存，关闭不必要的程序，检查是否有内存泄漏

📋 系统优化建议：
⚡ 紧急措施：
1. 立即处理严重告警项目
2. 监控系统稳定性
3. 准备应急预案

⏰ 建议监控频率：
- 每5分钟检查一次，直到问题解决
```

## 自定义配置

### 修改告警阈值
在 `agent_tool.py` 中修改相应的阈值：
```python
if cpu_usage > 60:  # 修改 CPU 告警阈值
if memory.percent > 60:  # 修改内存告警阈值
```

### 自定义 AI Prompt
编辑 `system_monitor_prompt.md` 或 `ai_system_monitor.py` 中的 prompt 模板。

### 添加新的监控项
在 `agent_tool.py` 的 `getSystemPerformance()` 函数中添加新的监控逻辑。

## 注意事项

1. **权限要求**: 某些系统信息需要管理员权限
2. **GPU 监控**: 需要安装相应的 GPU 驱动和库
3. **网络监控**: 某些网络信息可能需要特殊权限
4. **AI API**: 使用 AI 模型需要相应的 API 密钥和网络连接

## 故障排除

### 常见问题
1. **GPU 信息获取失败**: 检查 GPU 驱动和监控库安装
2. **权限不足**: 以管理员身份运行
3. **AI API 调用失败**: 检查 API 密钥和网络连接
4. **依赖库缺失**: 安装相应的 Python 包

### 调试模式
在代码中添加详细的错误信息输出：
```python
import traceback
try:
    # 你的代码
except Exception as e:
    print(f"错误: {str(e)}")
    traceback.print_exc()
```

## 扩展功能

- 添加历史数据存储和趋势分析
- 集成邮件/短信告警
- 开发 Web 界面
- 添加更多系统指标监控
- 集成日志分析功能
