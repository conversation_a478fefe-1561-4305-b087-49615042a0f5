import psutil
import json
import time
import os
from datetime import datetime
from typing import Dict, Any, List

try:
    import GPUtil
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False

try:
    import nvidia_ml_py3 as nvml
    NVML_AVAILABLE = True
except ImportError:
    NVML_AVAILABLE = False

def getSystemPerformance():
    """
    获取系统性能信息，包括CPU、内存、GPU、磁盘和网络状态
    如果检测到异常，会获取相关进程信息
    返回包含所有信息的字符串
    """

    def get_top_processes_by_cpu(limit=5):
        """获取CPU使用率最高的进程"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent']):
                try:
                    proc.info['cpu_percent'] = proc.cpu_percent()
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 按CPU使用率排序
            processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
            return processes[:limit]
        except Exception as e:
            return [{"error": f"Failed to get CPU processes: {str(e)}"}]

    def get_top_processes_by_memory(limit=5):
        """获取内存使用率最高的进程"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_percent']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 按内存使用率排序
            processes.sort(key=lambda x: x['memory_percent'], reverse=True)
            return processes[:limit]
        except Exception as e:
            return [{"error": f"Failed to get memory processes: {str(e)}"}]

    def format_bytes(bytes_value):
        """格式化字节数为可读格式"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} PB"

    # 收集系统信息
    system_info = []
    alerts = []

    # 获取当前时间
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    system_info.append(f"=== 系统性能监控报告 ({current_time}) ===\n")

    # 检查CPU使用率
    try:
        cpu_usage = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()

        system_info.append(f"CPU信息:")
        system_info.append(f"  - CPU核心数: {cpu_count}")
        if cpu_freq:
            system_info.append(f"  - CPU频率: {cpu_freq.current:.0f} MHz")
        system_info.append(f"  - CPU使用率: {cpu_usage:.1f}%")

        if cpu_usage > 60:
            alerts.append(f"CPU使用率异常: {cpu_usage:.1f}%")
            system_info.append(f"  - ⚠️  CPU使用率过高!")

            # 获取CPU使用率最高的进程
            top_cpu_processes = get_top_processes_by_cpu()
            system_info.append(f"  - CPU使用率最高的进程:")
            for i, proc in enumerate(top_cpu_processes, 1):
                if 'error' in proc:
                    system_info.append(f"    {i}. {proc['error']}")
                else:
                    system_info.append(f"    {i}. {proc['name']} (PID: {proc['pid']}) - {proc['cpu_percent']:.1f}%")

        system_info.append("")
    except Exception as e:
        alerts.append(f"CPU检查失败: {str(e)}")
        system_info.append(f"CPU信息: 获取失败 - {str(e)}\n")

    # 检查内存使用率
    try:
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()

        system_info.append(f"内存信息:")
        system_info.append(f"  - 总内存: {format_bytes(memory.total)}")
        system_info.append(f"  - 已使用: {format_bytes(memory.used)} ({memory.percent:.1f}%)")
        system_info.append(f"  - 可用内存: {format_bytes(memory.available)}")
        system_info.append(f"  - 交换分区: {format_bytes(swap.total)} (已使用: {swap.percent:.1f}%)")

        if memory.percent > 60:
            alerts.append(f"内存使用率异常: {memory.percent:.1f}%")
            system_info.append(f"  - ⚠️  内存使用率过高!")

            # 获取内存使用率最高的进程
            top_memory_processes = get_top_processes_by_memory()
            system_info.append(f"  - 内存使用率最高的进程:")
            for i, proc in enumerate(top_memory_processes, 1):
                if 'error' in proc:
                    system_info.append(f"    {i}. {proc['error']}")
                else:
                    system_info.append(f"    {i}. {proc['name']} (PID: {proc['pid']}) - {proc['memory_percent']:.1f}%")

        system_info.append("")
    except Exception as e:
        alerts.append(f"内存检查失败: {str(e)}")
        system_info.append(f"内存信息: 获取失败 - {str(e)}\n")

    # 检查GPU使用率
    try:
        gpu_info_found = False
        system_info.append(f"GPU信息:")

        # 尝试使用nvidia-ml-py3
        if NVML_AVAILABLE:
            try:
                nvml.nvmlInit()
                device_count = nvml.nvmlDeviceGetCount()
                gpu_info_found = True

                for i in range(device_count):
                    handle = nvml.nvmlDeviceGetHandleByIndex(i)
                    name = nvml.nvmlDeviceGetName(handle).decode('utf-8')
                    memory_info = nvml.nvmlDeviceGetMemoryInfo(handle)
                    utilization = nvml.nvmlDeviceGetUtilizationRates(handle)
                    temperature = nvml.nvmlDeviceGetTemperature(handle, nvml.NVML_TEMPERATURE_GPU)

                    gpu_usage = utilization.gpu
                    memory_usage = (memory_info.used / memory_info.total) * 100

                    system_info.append(f"  - GPU {i}: {name}")
                    system_info.append(f"    GPU使用率: {gpu_usage}%")
                    system_info.append(f"    显存使用率: {memory_usage:.1f}% ({format_bytes(memory_info.used)}/{format_bytes(memory_info.total)})")
                    system_info.append(f"    温度: {temperature}°C")

                    if gpu_usage > 60:
                        alerts.append(f"GPU {i} 使用率异常: {gpu_usage}%")
                        system_info.append(f"    ⚠️  GPU使用率过高!")
                    if memory_usage > 60:
                        alerts.append(f"GPU {i} 显存使用率异常: {memory_usage:.1f}%")
                        system_info.append(f"    ⚠️  显存使用率过高!")
                    if temperature > 60:
                        alerts.append(f"GPU {i} 温度异常: {temperature}°C")
                        system_info.append(f"    ⚠️  GPU温度过高!")

                nvml.nvmlShutdown()
            except Exception as e:
                system_info.append(f"  - NVML获取失败: {str(e)}")

        # 尝试使用GPUtil作为备选
        elif GPU_AVAILABLE:
            try:
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu_info_found = True
                    for i, gpu in enumerate(gpus):
                        gpu_usage = gpu.load * 100
                        memory_usage = (gpu.memoryUsed / gpu.memoryTotal) * 100
                        temperature = gpu.temperature

                        system_info.append(f"  - GPU {i}: {gpu.name}")
                        system_info.append(f"    GPU使用率: {gpu_usage:.1f}%")
                        system_info.append(f"    显存使用率: {memory_usage:.1f}% ({gpu.memoryUsed}MB/{gpu.memoryTotal}MB)")
                        system_info.append(f"    温度: {temperature}°C")

                        if gpu_usage > 60:
                            alerts.append(f"GPU {i} 使用率异常: {gpu_usage:.1f}%")
                            system_info.append(f"    ⚠️  GPU使用率过高!")
                        if memory_usage > 60:
                            alerts.append(f"GPU {i} 显存使用率异常: {memory_usage:.1f}%")
                            system_info.append(f"    ⚠️  显存使用率过高!")
                        if temperature > 60:
                            alerts.append(f"GPU {i} 温度异常: {temperature}°C")
                            system_info.append(f"    ⚠️  GPU温度过高!")
            except Exception as e:
                system_info.append(f"  - GPUtil获取失败: {str(e)}")

        if not gpu_info_found:
            system_info.append(f"  - 未检测到GPU或GPU监控库不可用")

        system_info.append("")
    except Exception as e:
        alerts.append(f"GPU检查失败: {str(e)}")
        system_info.append(f"GPU信息: 获取失败 - {str(e)}\n")

    # 检查磁盘使用率
    try:
        system_info.append(f"磁盘信息:")
        partitions = psutil.disk_partitions()
        disk_alerts = []

        for partition in partitions:
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage = (usage.used / usage.total) * 100

                system_info.append(f"  - 分区: {partition.mountpoint} ({partition.fstype})")
                system_info.append(f"    总容量: {format_bytes(usage.total)}")
                system_info.append(f"    已使用: {format_bytes(usage.used)} ({disk_usage:.1f}%)")
                system_info.append(f"    可用空间: {format_bytes(usage.free)}")

                if disk_usage > 60:
                    disk_alerts.append(f"磁盘 {partition.mountpoint} 使用率异常: {disk_usage:.1f}%")
                    system_info.append(f"    ⚠️  磁盘使用率过高!")

            except PermissionError:
                system_info.append(f"  - 分区: {partition.mountpoint} - 权限不足，无法访问")
            except Exception as e:
                system_info.append(f"  - 分区: {partition.mountpoint} - 获取失败: {str(e)}")

        alerts.extend(disk_alerts)

        # 磁盘IO信息
        try:
            disk_io = psutil.disk_io_counters()
            if disk_io:
                system_info.append(f"  - 磁盘IO统计:")
                system_info.append(f"    读取: {format_bytes(disk_io.read_bytes)}")
                system_info.append(f"    写入: {format_bytes(disk_io.write_bytes)}")
                system_info.append(f"    读取次数: {disk_io.read_count}")
                system_info.append(f"    写入次数: {disk_io.write_count}")
        except Exception as e:
            system_info.append(f"  - 磁盘IO信息获取失败: {str(e)}")

        system_info.append("")
    except Exception as e:
        alerts.append(f"磁盘检查失败: {str(e)}")
        system_info.append(f"磁盘信息: 获取失败 - {str(e)}\n")

    # 检查网络状态
    try:
        system_info.append(f"网络信息:")

        # 网络IO统计
        net_io = psutil.net_io_counters()
        if net_io:
            system_info.append(f"  - 网络IO统计:")
            system_info.append(f"    发送: {format_bytes(net_io.bytes_sent)}")
            system_info.append(f"    接收: {format_bytes(net_io.bytes_recv)}")
            system_info.append(f"    发送包数: {net_io.packets_sent}")
            system_info.append(f"    接收包数: {net_io.packets_recv}")
            system_info.append(f"    发送错误: {net_io.errout}")
            system_info.append(f"    接收错误: {net_io.errin}")
            system_info.append(f"    发送丢包: {net_io.dropout}")
            system_info.append(f"    接收丢包: {net_io.dropin}")

            # 检查网络异常
            network_alerts = []
            if net_io.errin > 100:
                network_alerts.append(f"网络接收错误过多: {net_io.errin}")
                system_info.append(f"    ⚠️  网络接收错误过多!")
            if net_io.errout > 100:
                network_alerts.append(f"网络发送错误过多: {net_io.errout}")
                system_info.append(f"    ⚠️  网络发送错误过多!")
            if net_io.dropin > 100:
                network_alerts.append(f"网络接收丢包过多: {net_io.dropin}")
                system_info.append(f"    ⚠️  网络接收丢包过多!")
            if net_io.dropout > 100:
                network_alerts.append(f"网络发送丢包过多: {net_io.dropout}")
                system_info.append(f"    ⚠️  网络发送丢包过多!")

            alerts.extend(network_alerts)

        # 网络连接信息
        try:
            connections = psutil.net_connections()
            established_count = len([conn for conn in connections if conn.status == 'ESTABLISHED'])
            listen_count = len([conn for conn in connections if conn.status == 'LISTEN'])

            system_info.append(f"  - 网络连接:")
            system_info.append(f"    已建立连接: {established_count}")
            system_info.append(f"    监听端口: {listen_count}")
            system_info.append(f"    总连接数: {len(connections)}")

            if established_count > 1000:
                alerts.append(f"网络连接数过多: {established_count}")
                system_info.append(f"    ⚠️  网络连接数过多!")

        except Exception as e:
            system_info.append(f"  - 网络连接信息获取失败: {str(e)}")

        system_info.append("")
    except Exception as e:
        alerts.append(f"网络检查失败: {str(e)}")
        system_info.append(f"网络信息: 获取失败 - {str(e)}\n")

    # 生成总结报告
    system_info.append("=== 监控总结 ===")
    if alerts:
        system_info.append("⚠️  发现以下异常:")
        for i, alert in enumerate(alerts, 1):
            system_info.append(f"  {i}. {alert}")
    else:
        system_info.append("✅ 系统运行正常，未发现异常")

    system_info.append(f"\n报告生成时间: {current_time}")
    system_info.append("=" * 50)

    # 返回完整的系统信息字符串
    return "\n".join(system_info)