import psutil
import json
import time
import os
from datetime import datetime
from typing import Dict, Any, List

try:
    import GPUtil
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False

try:
    import nvidia_ml_py3 as nvml
    NVML_AVAILABLE = True
except ImportError:
    NVML_AVAILABLE = False

def getSystemPerformance():
    def check_system_performance():
        alerts = [] 
        
        try:
            cpu_usage = psutil.cpu_percent(interval=1)
            if cpu_usage > 80:
                alerts.append(f"cpu out of {cpu_usage}%")
        except:
            alerts.append("cpu check failed")
        
        # Check Memory
        try:
            memory = psutil.virtual_memory()
            if memory.percent > 80:
                alerts.append(f"memory out of {memory.percent}%")
        except:
            alerts.append("memory check failed")
        
        # Check GPU
        try:
            if NVML_AVAILABLE:
                nvml.nvmlInit()
                device_count = nvml.nvmlDeviceGetCount()
                for i in range(device_count):
                    handle = nvml.nvmlDeviceGetHandleByIndex(i)
                    memory_info = nvml.nvmlDeviceGetMemoryInfo(handle)
                    utilization = nvml.nvmlDeviceGetUtilizationRates(handle)
                    temperature = nvml.nvmlDeviceGetTemperature(handle, nvml.NVML_TEMPERATURE_GPU)
                    
                    gpu_usage = utilization.gpu
                    memory_usage = (memory_info.used / memory_info.total) * 100
                    
                    if gpu_usage > 80:
                        alerts.append(f"gpu utilization out of {gpu_usage}%")
                    if memory_usage > 80:
                        alerts.append(f"gpu memory out of {memory_usage:.1f}%")
                    if temperature > 80:
                        alerts.append(f"gpu temperature out of {temperature}°C")
                nvml.nvmlShutdown()
            elif GPU_AVAILABLE:
                gpus = GPUtil.getGPUs()
                for gpu in gpus:
                    gpu_usage = gpu.load * 100
                    memory_usage = (gpu.memoryUsed / gpu.memoryTotal) * 100
                    temperature = gpu.temperature
                    
                    if gpu_usage > 80:
                        alerts.append(f"gpu utilization out of {gpu_usage:.1f}%")
                    if memory_usage > 80:
                        alerts.append(f"gpu memory out of {memory_usage:.1f}%")
                    if temperature > 80:
                        alerts.append(f"gpu temperature out of {temperature}°C")
        except:
            pass  # GPU monitoring is optional
        
        # Check Disk
        try:
            partitions = psutil.disk_partitions()
            for partition in partitions:
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_usage = (usage.used / usage.total) * 100
                    if disk_usage > 80:
                        alerts.append(f"disk out of {disk_usage:.1f}% ({partition.mountpoint})")
                except PermissionError:
                    continue
        except:
            alerts.append("disk check failed")
        
        # Check Network errors
        try:
            net_io = psutil.net_io_counters()
            if net_io.errin > 100:
                alerts.append(f"network errors in: {net_io.errin}")
            if net_io.errout > 100:
                alerts.append(f"network errors out: {net_io.errout}")
            if net_io.dropin > 100:
                alerts.append(f"network drops in: {net_io.dropin}")
            if net_io.dropout > 100:
                alerts.append(f"network drops out: {net_io.dropout}")
        except:
            alerts.append("network check failed")
        
        return alerts