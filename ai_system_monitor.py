#!/usr/bin/env python3
"""
AI 系统监控 - 集成大模型 API 进行智能分析
"""

from agent_tool import getSystemPerformance
import json
from datetime import datetime

# 系统监控分析的 Prompt 模板
SYSTEM_MONITOR_PROMPT = """你是一个专业的系统监控分析专家。请分析以下系统性能数据，并提供智能告警和建议。

分析要求：
1. 识别潜在的系统问题并按严重程度分类
2. 提供具体的解决建议和优化方案
3. 使用清晰、易懂的语言

严重程度标准：
🔴 严重告警: CPU>90%, 内存>90%, GPU温度>85°C, 磁盘>95%, 大量网络错误
🟡 警告: CPU 60-90%, 内存 60-90%, GPU使用率>80%或温度60-85°C, 磁盘60-95%
🟢 正常: 所有指标在正常范围内

输出格式要求：
- 如果发现问题：提供详细分析、影响说明和具体建议
- 如果系统正常：简洁回复"✅ 系统运行正常"
- 重点关注对用户影响最大的问题
- 提供可操作的具体解决方案

系统数据：
{system_data}

请基于以上数据进行分析："""

def analyze_with_ai_model(system_data, ai_api_function=None):
    """
    使用 AI 模型分析系统数据
    
    Args:
        system_data: 从 getSystemPerformance() 获取的系统数据
        ai_api_function: AI 模型 API 调用函数（可选）
    
    Returns:
        str: AI 分析结果
    """
    
    # 构建完整的 prompt
    full_prompt = SYSTEM_MONITOR_PROMPT.format(system_data=system_data)
    
    if ai_api_function:
        # 如果提供了 AI API 函数，使用它进行分析
        try:
            result = ai_api_function(full_prompt)
            return result
        except Exception as e:
            return f"❌ AI 分析失败: {str(e)}"
    else:
        # 如果没有提供 AI API，返回 prompt 供手动使用
        return f"""
=== AI 分析 Prompt ===
以下是完整的 prompt，你可以将其复制到任何 AI 模型中进行分析：

{full_prompt}

=== 使用说明 ===
1. 复制上述 prompt 到 ChatGPT、Claude 或其他 AI 模型
2. AI 将分析系统数据并提供智能告警和建议
3. 根据 AI 的建议采取相应的优化措施
"""

# 示例：集成不同 AI 模型的函数模板

def call_openai_api(prompt):
    """
    调用 OpenAI API 的示例函数
    需要安装: pip install openai
    """
    try:
        import openai
        
        # 设置 API 密钥
        # openai.api_key = "your-api-key-here"
        
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",  # 或 "gpt-4"
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=1000,
            temperature=0.3
        )
        
        return response.choices[0].message.content
    except Exception as e:
        return f"OpenAI API 调用失败: {str(e)}"

def call_claude_api(prompt):
    """
    调用 Claude API 的示例函数
    需要安装: pip install anthropic
    """
    try:
        import anthropic
        
        # 设置 API 密钥
        # client = anthropic.Anthropic(api_key="your-api-key-here")
        
        # response = client.messages.create(
        #     model="claude-3-sonnet-20240229",
        #     max_tokens=1000,
        #     messages=[
        #         {"role": "user", "content": prompt}
        #     ]
        # )
        
        # return response.content[0].text
        return "Claude API 未配置"
    except Exception as e:
        return f"Claude API 调用失败: {str(e)}"

def call_local_llm_api(prompt):
    """
    调用本地 LLM API 的示例函数（如 Ollama）
    """
    try:
        import requests
        
        # 本地 Ollama API 示例
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama2",  # 或其他本地模型
                "prompt": prompt,
                "stream": False
            }
        )
        
        if response.status_code == 200:
            return response.json().get("response", "无响应")
        else:
            return f"本地 API 调用失败: {response.status_code}"
    except Exception as e:
        return f"本地 LLM API 调用失败: {str(e)}"

def main():
    """
    主函数 - 演示如何使用 AI 进行系统监控分析
    """
    print("🤖 AI 系统监控分析启动...")
    print("=" * 60)
    
    try:
        # 1. 获取系统性能数据
        print("📊 正在收集系统数据...")
        system_data = getSystemPerformance()
        
        print("✅ 系统数据收集完成")
        print(f"数据大小: {len(system_data)} 字符")
        
        # 2. 使用 AI 进行分析
        print("\n🔍 正在进行 AI 分析...")
        
        # 选择 AI 模型进行分析（取消注释相应的行）
        # analysis_result = analyze_with_ai_model(system_data, call_openai_api)
        # analysis_result = analyze_with_ai_model(system_data, call_claude_api)
        # analysis_result = analyze_with_ai_model(system_data, call_local_llm_api)
        
        # 如果没有配置 AI API，返回 prompt 供手动使用
        analysis_result = analyze_with_ai_model(system_data)
        
        # 3. 输出分析结果
        print("\n" + "=" * 60)
        print("🤖 AI 分析结果：")
        print("=" * 60)
        print(analysis_result)
        print("=" * 60)
        
        # 4. 可选：保存结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存原始系统数据
        with open(f"system_data_{timestamp}.txt", 'w', encoding='utf-8') as f:
            f.write(system_data)
        
        # 保存 AI 分析结果
        with open(f"ai_analysis_{timestamp}.txt", 'w', encoding='utf-8') as f:
            f.write(analysis_result)
        
        print(f"\n💾 结果已保存到文件:")
        print(f"- 系统数据: system_data_{timestamp}.txt")
        print(f"- AI 分析: ai_analysis_{timestamp}.txt")
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
