#!/usr/bin/env python3
"""
系统监控 Agent - 使用 AI 分析系统性能数据并提供智能告警
"""

from agent_tool import getSystemPerformance
import json
import re
from datetime import datetime

class SystemMonitorAgent:
    def __init__(self):
        self.system_prompt = """你是一个专业的系统监控分析专家，负责分析系统性能数据并提供智能告警和建议。

你的任务：
1. 分析系统性能数据，识别潜在问题
2. 按严重程度分类问题（严重🔴/警告🟡/正常🟢）
3. 提供具体的解决建议和优化方案
4. 使用清晰、易懂的语言描述问题

严重程度标准：
🔴 严重告警: CPU>90%, 内存>90%, GPU温度>85°C, 磁盘>95%, 大量网络错误
🟡 警告: CPU 60-90%, 内存 60-90%, GPU使用率>80%或温度60-85°C, 磁盘60-95%
🟢 正常: 所有指标在正常范围内

输出格式：
- 如果有问题：提供详细分析和建议
- 如果正常：简洁回复"✅ 系统运行正常"
- 重点关注影响最大的问题
- 提供可操作的具体建议"""

    def analyze_system_data(self, system_data):
        """
        分析系统数据并生成智能告警
        """
        try:
            # 解析系统数据中的关键信息
            analysis_result = self._parse_system_data(system_data)
            
            # 生成分析报告
            report = self._generate_report(analysis_result, system_data)
            
            return report
            
        except Exception as e:
            return f"❌ 系统分析失败: {str(e)}"
    
    def _parse_system_data(self, data):
        """
        解析系统数据，提取关键指标
        """
        result = {
            'alerts': [],
            'warnings': [],
            'critical': [],
            'normal': True
        }
        
        lines = data.split('\n')
        
        for line in lines:
            if '⚠️' in line:
                result['normal'] = False
                # 根据内容判断严重程度
                if any(keyword in line.lower() for keyword in ['cpu使用率过高', '内存使用率过高', '温度过高', '磁盘使用率过高']):
                    # 提取具体数值
                    numbers = re.findall(r'(\d+\.?\d*)%', line)
                    if numbers:
                        value = float(numbers[0])
                        if value > 90:
                            result['critical'].append(line.strip())
                        else:
                            result['warnings'].append(line.strip())
                    else:
                        result['warnings'].append(line.strip())
                else:
                    result['warnings'].append(line.strip())
        
        return result
    
    def _generate_report(self, analysis, raw_data):
        """
        生成分析报告
        """
        if analysis['normal']:
            return "✅ 系统运行正常\n所有性能指标均在正常范围内，无需特别关注。"
        
        report = []
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report.append(f"🖥️ 系统监控报告 - {current_time}")
        report.append("=" * 50)
        
        # 严重告警
        if analysis['critical']:
            report.append("\n🔴 严重告警：")
            for alert in analysis['critical']:
                clean_alert = alert.replace('⚠️', '').strip()
                report.append(f"- {clean_alert}")
                report.append(f"  💡 建议：{self._get_suggestion(clean_alert, 'critical')}")
        
        # 警告
        if analysis['warnings']:
            report.append("\n🟡 警告：")
            for warning in analysis['warnings']:
                clean_warning = warning.replace('⚠️', '').strip()
                report.append(f"- {clean_warning}")
                report.append(f"  💡 建议：{self._get_suggestion(clean_warning, 'warning')}")
        
        # 添加详细建议
        report.append(self._get_detailed_suggestions(analysis))
        
        return "\n".join(report)
    
    def _get_suggestion(self, alert, severity):
        """
        根据告警类型提供建议
        """
        alert_lower = alert.lower()
        
        if 'cpu' in alert_lower:
            if severity == 'critical':
                return "立即检查CPU占用最高的进程，考虑结束不必要的程序或增加CPU资源"
            else:
                return "监控CPU使用情况，关闭不必要的应用程序"
        
        elif '内存' in alert_lower or 'memory' in alert_lower:
            if severity == 'critical':
                return "立即释放内存，关闭内存占用大的程序，考虑增加内存"
            else:
                return "清理内存，关闭不必要的程序，检查是否有内存泄漏"
        
        elif 'gpu' in alert_lower:
            if '温度' in alert_lower:
                return "检查GPU散热，清理灰尘，降低GPU负载"
            else:
                return "监控GPU使用情况，优化GPU密集型任务"
        
        elif '磁盘' in alert_lower or 'disk' in alert_lower:
            if severity == 'critical':
                return "立即清理磁盘空间，删除不必要文件，考虑扩容"
            else:
                return "清理临时文件，整理磁盘空间"
        
        elif '网络' in alert_lower or 'network' in alert_lower:
            return "检查网络连接，重启网络设备，检查网络配置"
        
        else:
            return "请检查相关系统组件，必要时联系技术支持"
    
    def _get_detailed_suggestions(self, analysis):
        """
        提供详细的优化建议
        """
        suggestions = ["\n📋 系统优化建议："]
        
        if analysis['critical']:
            suggestions.append("⚡ 紧急措施：")
            suggestions.append("1. 立即处理严重告警项目")
            suggestions.append("2. 监控系统稳定性")
            suggestions.append("3. 准备应急预案")
        
        if analysis['warnings']:
            suggestions.append("🔧 优化措施：")
            suggestions.append("1. 定期清理系统垃圾文件")
            suggestions.append("2. 优化开机启动项")
            suggestions.append("3. 更新系统和驱动程序")
            suggestions.append("4. 考虑硬件升级")
        
        suggestions.append("\n⏰ 建议监控频率：")
        if analysis['critical']:
            suggestions.append("- 每5分钟检查一次，直到问题解决")
        elif analysis['warnings']:
            suggestions.append("- 每30分钟检查一次")
        else:
            suggestions.append("- 每小时检查一次")
        
        return "\n".join(suggestions)

def main():
    """
    主函数 - 运行系统监控分析
    """
    print("🔍 启动系统监控分析...")
    
    try:
        # 获取系统性能数据
        print("📊 正在收集系统数据...")
        system_data = getSystemPerformance()
        
        # 创建监控代理
        agent = SystemMonitorAgent()
        
        # 分析数据并生成报告
        print("🤖 正在分析数据...")
        report = agent.analyze_system_data(system_data)
        
        # 输出报告
        print("\n" + "="*60)
        print(report)
        print("="*60)
        
        # 如果需要，也可以保存原始数据
        # with open(f"system_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 'w', encoding='utf-8') as f:
        #     f.write(system_data)
        
    except Exception as e:
        print(f"❌ 监控分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
