#!/usr/bin/env python3
"""
测试系统性能监控功能
"""

from agent_tool import getSystemPerformance

def main():
    print("开始系统性能监控测试...")
    print("=" * 60)
    
    try:
        # 调用系统性能监控函数
        result = getSystemPerformance()
        
        # 打印结果
        print(result)
        
        print("\n" + "=" * 60)
        print("测试完成!")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
